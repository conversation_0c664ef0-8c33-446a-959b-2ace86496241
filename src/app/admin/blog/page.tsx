import { But<PERSON> } from "@/components/ui/button"
import { PageLayout } from "../../../components/page-layout"
import { BlogList } from "./components/blog-list"
import { Plus, Tag } from "lucide-react"

export default function BlogPage() {
  return (
    <PageLayout
      title="Blog Posts"
      description="Manage your blog posts, categories, and tags"
      actions={
        <>
          <Button variant="outline" className="mr-2">
            <Tag className="mr-2 h-4 w-4" />
            Manage Categories
          </Button>
          <Button>
            <Plus className="mr-2 h-4 w-4" />
            New Post
          </Button>
        </>
      }
    >
      <BlogList />
    </PageLayout>
  )
}
