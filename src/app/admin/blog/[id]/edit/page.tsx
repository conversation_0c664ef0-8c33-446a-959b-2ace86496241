'use client'

import "../../styles.css"
import { useRouter } from "next/navigation"
import { useEffect, useState, use } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { toast } from "sonner"
import { Editor } from "@/components/editor"

interface BlogPost {
  id: string
  title: string
  slug: string
  excerpt: string
  content: string
  featuredImage: string
  published: boolean
  categories: string[]
  metaTitle?: string
  metaDescription?: string
  tags: string[]
  readTime: number
  createdAt: string
  updatedAt: string
}

export default function EditBlogPostPage({ params }: { params: { id: string } }) {
  const router = useRouter()
  const [post, setPost] = useState<BlogPost | null>(null)
  const [loading, setLoading] = useState(true)
  // Unwrap params with React.use()
  const unwrappedParams = use(params)
  const postId = unwrappedParams.id

  useEffect(() => {
    // In a real app, you would fetch the blog post data here
    // For now, we'll use mock data
    const fetchPost = async () => {
      try {
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 500))
        
        // Mock data - replace with actual API call
        const mockPost: BlogPost = {
          id: postId,
          title: "Getting Started with Next.js",
          slug: "getting-started-with-nextjs",
          excerpt: "Learn the basics of Next.js and how to build modern web applications.",
          content: "<h1>Getting Started with Next.js</h1><p>Next.js is a React framework that enables server-side rendering and static site generation.</p>",
          featuredImage: "https://images.unsplash.com/photo-1633356122544-f134324a6cee?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1470&q=80",
          published: true,
          categories: ["web-dev", "programming"],
          tags: ["nextjs", "react", "javascript"],
          readTime: 5,
          metaTitle: "Getting Started with Next.js - A Comprehensive Guide",
          metaDescription: "Learn how to get started with Next.js and build modern web applications with React.",
          createdAt: "2023-05-15T10:30:00Z",
          updatedAt: new Date().toISOString(),
        }
        
        setPost(mockPost)
      } catch (error) {
        console.error("Error fetching post:", error)
        toast.error("Failed to load the blog post. Please try again.")
      } finally {
        setLoading(false)
      }
    }

    fetchPost()
  }, [postId])

  const handleSuccess = () => {
    toast("Your blog post has been updated successfully.")
    router.push("/admin/blog")
  }

  if (loading) {
    return (
      <div className="container py-6 flex items-center justify-center min-h-[50vh]">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    )
  }

  if (!post) {
    return (
      <div className="container py-6">
        <div className="text-center py-12">
          <h2 className="text-2xl font-bold">Post not found</h2>
          <p className="text-muted-foreground mt-2">The requested blog post could not be found.</p>
          <Button
            variant="outline" 
            className="mt-4"
            onClick={() => router.push('/admin/blog')}
          >
            Back to Blog Posts
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="container py-6 space-y-6">
      <Editor 
        content={post.content}
        onChange={(content) => {
          setPost({...post, content});
        }}
      />
    </div>
  )
}
