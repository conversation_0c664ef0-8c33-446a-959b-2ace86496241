'use client'

import "../styles.css"
import { useRouter } from "next/navigation"
import { useEffect, useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { toast } from "sonner"
import { Editor } from "@/components/editor"

interface BlogPost {
  id: string
  title: string
  slug: string
  excerpt: string
  content: string
  featuredImage: string
  published: boolean
  categories: string[]
  metaTitle?: string
  metaDescription?: string
  tags: string[]
  readTime: number
  createdAt: string
  updatedAt: string
}

const initialNewPost: BlogPost = {
  id: "", // Will be generated on the server
  title: "New Blog Post Title",
  slug: "new-blog-post-slug",
  excerpt: "A brief summary of your new blog post.",
  content: "<h1>Start writing your new blog post here!</h1>",
  featuredImage: "",
  published: false,
  categories: [],
  tags: [],
  readTime: 0,
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
}

export default function NewBlogPostPage() {
  const router = useRouter()
  const [newPost, setNewPost] = useState<BlogPost>(initialNewPost)
  const [loading, setLoading] = useState(false) // For saving state

  const handleCreatePost = async () => {
    setLoading(true)
    try {
      // Simulate API call for creating a new post
      await new Promise(resolve => setTimeout(resolve, 1000))
      // In a real app, you would send newPost data to your API
      console.log("Creating new post:", newPost)
      toast("Your blog post has been created successfully.")
      router.push("/admin/blog")
    } catch (error) {
      console.error("Error creating post:", error)
      toast.error("Failed to create the blog post. Please try again.")
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="container py-6 space-y-6">
      <div className="space-y-2">
        <h1 className="text-3xl font-bold">New Blog Post</h1>
        <p className="text-muted-foreground">
          Create a new blog post with rich content and media
        </p>
      </div>
      <Editor
        content={newPost.content}
        onChange={(content) => setNewPost({ ...newPost, content })}
      />
      <Button onClick={handleCreatePost} disabled={loading}>
        {loading ? "Creating..." : "Create Blog Post"}
      </Button>
    </div>
  )
}
