import Layout from "@/components/layout/Layout";
import SectionHeader from "@/components/layout/SectionHeader";
import Section1 from "@/components/sections/blog/Section1";
import Section2 from "@/components/sections/about/Section3";
import type { Metadata } from "next";

export const metadata: Metadata = {
  title: "Industry Insights | Motshwanelo IT Consulting - Latest Technology Trends & News",
  description: "Stay updated with the latest IT trends, digital transformation insights, and technology news from Motshwanelo IT Consulting. Expert analysis on Smart Cities, Data Centres, and enterprise solutions.",
  keywords: ["IT Blog", "Technology News", "Digital Transformation", "Smart City Trends", "Data Centre News", "IT Industry Insights"],
  openGraph: {
    title: "Industry Insights | Latest Technology Trends & News",
    description: "Stay updated with expert insights on digital transformation, Smart Cities, Data Centres, and the latest IT industry trends.",
    type: "website",
    url: "https://motshwaneloitconsulting.co.za/blog",
  },
};

export default function Blog() {
    return (
        <>
            <Layout>
                <SectionHeader title="Industry Insights & News" group_page="Latest Technology Trends from Our Experts" current_page="Blog" display="d-none" />
                <Section1 />
                <Section2 />
            </Layout>
        </>
    );
}
