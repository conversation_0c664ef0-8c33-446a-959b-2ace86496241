"use client";

import Layout from "@/components/layout/Layout";
import SectionHeader from "@/components/layout/SectionHeader";

export default function SoftwareDevelopmentPage() {
    return (
        <>
            <Layout>
                <SectionHeader 
                    title="Custom Software Development" 
                    group_page="Scalable Applications Built with Cutting-Edge Technologies" 
                    current_page="Software Development" 
                    display="d-none" 
                />
                
                <div className="service-details-section sp">
                    <div className="container">
                        <div className="row">
                            <div className="col-lg-8">
                                <div className="service-details-content">
                                    <h2>💻 Enterprise Software Development</h2>
                                    <p className="lead">
                                        Transform your business operations with custom software solutions designed and developed 
                                        specifically for your unique requirements. From enterprise web applications to mobile solutions, 
                                        we leverage cutting-edge technologies to build scalable, secure, and user-friendly software 
                                        that drives business growth.
                                    </p>
                                    
                                    <div className="development-services mt-4">
                                        <div className="row">
                                            <div className="col-md-6 mb-4">
                                                <div className="dev-service-item">
                                                    <div className="icon">
                                                        <i className="fas fa-globe text-primary"></i>
                                                    </div>
                                                    <h4>Web Application Development</h4>
                                                    <p>Modern, responsive web applications built with the latest frameworks and technologies.</p>
                                                    <div className="tech-stack">
                                                        <span>React</span>
                                                        <span>Next.js</span>
                                                        <span>Node.js</span>
                                                        <span>TypeScript</span>
                                                        <span>PostgreSQL</span>
                                                    </div>
                                                </div>
                                            </div>
                                            
                                            <div className="col-md-6 mb-4">
                                                <div className="dev-service-item">
                                                    <div className="icon">
                                                        <i className="fas fa-mobile-alt text-primary"></i>
                                                    </div>
                                                    <h4>Mobile App Development</h4>
                                                    <p>Cross-platform mobile applications that provide seamless user experiences across devices.</p>
                                                    <div className="tech-stack">
                                                        <span>React Native</span>
                                                        <span>Flutter</span>
                                                        <span>iOS</span>
                                                        <span>Android</span>
                                                        <span>PWA</span>
                                                    </div>
                                                </div>
                                            </div>
                                            
                                            <div className="col-md-6 mb-4">
                                                <div className="dev-service-item">
                                                    <div className="icon">
                                                        <i className="fas fa-plug text-primary"></i>
                                                    </div>
                                                    <h4>API Development & Integration</h4>
                                                    <p>Robust APIs and seamless integrations that connect your systems and enable data flow.</p>
                                                    <div className="tech-stack">
                                                        <span>REST APIs</span>
                                                        <span>GraphQL</span>
                                                        <span>Microservices</span>
                                                        <span>Webhooks</span>
                                                        <span>OAuth</span>
                                                    </div>
                                                </div>
                                            </div>
                                            
                                            <div className="col-md-6 mb-4">
                                                <div className="dev-service-item">
                                                    <div className="icon">
                                                        <i className="fas fa-building text-primary"></i>
                                                    </div>
                                                    <h4>Enterprise Solutions</h4>
                                                    <p>Large-scale enterprise applications designed for complex business processes and workflows.</p>
                                                    <div className="tech-stack">
                                                        <span>ERP Systems</span>
                                                        <span>CRM Solutions</span>
                                                        <span>Business Intelligence</span>
                                                        <span>Workflow Automation</span>
                                                        <span>Data Analytics</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div className="development-process mt-5">
                                        <h3>Our Development Process</h3>
                                        <div className="process-flow">
                                            <div className="process-step">
                                                <div className="step-icon">
                                                    <i className="fas fa-lightbulb"></i>
                                                </div>
                                                <h4>Discovery & Planning</h4>
                                                <p>Requirements analysis, technical planning, and project roadmap creation</p>
                                            </div>
                                            <div className="process-arrow">→</div>
                                            <div className="process-step">
                                                <div className="step-icon">
                                                    <i className="fas fa-pencil-ruler"></i>
                                                </div>
                                                <h4>Design & Prototyping</h4>
                                                <p>UI/UX design, wireframing, and interactive prototypes</p>
                                            </div>
                                            <div className="process-arrow">→</div>
                                            <div className="process-step">
                                                <div className="step-icon">
                                                    <i className="fas fa-code"></i>
                                                </div>
                                                <h4>Development & Testing</h4>
                                                <p>Agile development with continuous testing and quality assurance</p>
                                            </div>
                                            <div className="process-arrow">→</div>
                                            <div className="process-step">
                                                <div className="step-icon">
                                                    <i className="fas fa-rocket"></i>
                                                </div>
                                                <h4>Deployment & Support</h4>
                                                <p>Production deployment, training, and ongoing maintenance</p>
                                            </div>
                                        </div>
                                    </div>

                                    <div className="why-choose-us mt-5">
                                        <h3>Why Choose Our Development Team?</h3>
                                        <div className="row">
                                            <div className="col-md-4 mb-3">
                                                <div className="advantage-item">
                                                    <i className="fas fa-users text-primary"></i>
                                                    <h4>Expert Team</h4>
                                                    <p>Experienced developers with expertise in modern technologies and best practices</p>
                                                </div>
                                            </div>
                                            <div className="col-md-4 mb-3">
                                                <div className="advantage-item">
                                                    <i className="fas fa-sync-alt text-primary"></i>
                                                    <h4>Agile Methodology</h4>
                                                    <p>Iterative development with regular feedback and continuous improvement</p>
                                                </div>
                                            </div>
                                            <div className="col-md-4 mb-3">
                                                <div className="advantage-item">
                                                    <i className="fas fa-shield-alt text-primary"></i>
                                                    <h4>Secure & Scalable</h4>
                                                    <p>Built with security-first approach and designed to scale with your business</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div className="col-lg-4">
                                <div className="service-sidebar">
                                    <div className="sidebar-widget">
                                        <h3>Start Your Project</h3>
                                        <div className="contact-info">
                                            <p><i className="fas fa-phone"></i> +27 11 4 9 20992</p>
                                            <p><i className="fas fa-envelope"></i> <EMAIL></p>
                                        </div>
                                        <a href="/contact" className="theme-btn1 w-100 text-center">
                                            Get Project Quote
                                            <span><i className="fas fa-arrow-right"></i></span>
                                        </a>
                                    </div>
                                    
                                    <div className="sidebar-widget">
                                        <h3>Development Stats</h3>
                                        <div className="stats-grid">
                                            <div className="stat-item">
                                                <h4>100+</h4>
                                                <p>Applications Built</p>
                                            </div>
                                            <div className="stat-item">
                                                <h4>15+</h4>
                                                <p>Technologies Mastered</p>
                                            </div>
                                            <div className="stat-item">
                                                <h4>99%</h4>
                                                <p>Client Satisfaction</p>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div className="sidebar-widget">
                                        <h3>Technologies We Use</h3>
                                        <div className="technology-grid">
                                            <div className="tech-category">
                                                <h5>Frontend</h5>
                                                <span>React</span>
                                                <span>Next.js</span>
                                                <span>TypeScript</span>
                                                <span>Vue.js</span>
                                            </div>
                                            <div className="tech-category">
                                                <h5>Backend</h5>
                                                <span>Node.js</span>
                                                <span>Python</span>
                                                <span>Java</span>
                                                <span>.NET</span>
                                            </div>
                                            <div className="tech-category">
                                                <h5>Database</h5>
                                                <span>PostgreSQL</span>
                                                <span>MongoDB</span>
                                                <span>MySQL</span>
                                                <span>Redis</span>
                                            </div>
                                            <div className="tech-category">
                                                <h5>Cloud</h5>
                                                <span>AWS</span>
                                                <span>Azure</span>
                                                <span>Docker</span>
                                                <span>Kubernetes</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <style jsx>{`
                    .dev-service-item {
                        background: #f8f9fa;
                        padding: 25px;
                        border-radius: 10px;
                        height: 100%;
                        border-left: 4px solid #03276e;
                    }
                    
                    .dev-service-item .icon {
                        font-size: 2.5rem;
                        margin-bottom: 15px;
                    }
                    
                    .tech-stack {
                        display: flex;
                        flex-wrap: wrap;
                        gap: 6px;
                        margin-top: 15px;
                    }
                    
                    .tech-stack span {
                        background: #03276e;
                        color: white;
                        padding: 3px 8px;
                        border-radius: 12px;
                        font-size: 0.75rem;
                    }
                    
                    .process-flow {
                        display: flex;
                        align-items: center;
                        flex-wrap: wrap;
                        gap: 20px;
                        margin-top: 20px;
                    }
                    
                    .process-step {
                        flex: 1;
                        text-align: center;
                        min-width: 200px;
                    }
                    
                    .step-icon {
                        width: 60px;
                        height: 60px;
                        border-radius: 50%;
                        background: #03276e;
                        color: white;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        margin: 0 auto 15px;
                        font-size: 1.5rem;
                    }
                    
                    .process-arrow {
                        font-size: 1.5rem;
                        color: #03276e;
                        font-weight: bold;
                    }
                    
                    .advantage-item {
                        text-align: center;
                        padding: 20px;
                    }
                    
                    .advantage-item i {
                        font-size: 2.5rem;
                        margin-bottom: 15px;
                    }
                    
                    .service-sidebar {
                        padding-left: 30px;
                    }
                    
                    .sidebar-widget {
                        background: #f8f9fa;
                        padding: 25px;
                        border-radius: 10px;
                        margin-bottom: 30px;
                    }
                    
                    .contact-info p {
                        margin-bottom: 10px;
                        display: flex;
                        align-items: center;
                        gap: 10px;
                    }
                    
                    .stats-grid {
                        display: grid;
                        grid-template-columns: 1fr 1fr;
                        gap: 15px;
                        text-align: center;
                    }
                    
                    .stat-item h4 {
                        font-size: 1.8rem;
                        color: #03276e;
                        margin-bottom: 5px;
                    }
                    
                    .technology-grid {
                        display: grid;
                        gap: 15px;
                    }
                    
                    .tech-category h5 {
                        margin-bottom: 8px;
                        color: #03276e;
                        font-weight: bold;
                    }
                    
                    .tech-category span {
                        display: inline-block;
                        background: #e9ecef;
                        padding: 4px 8px;
                        border-radius: 4px;
                        font-size: 0.8rem;
                        margin: 2px;
                    }
                    
                    @media (max-width: 768px) {
                        .process-flow {
                            flex-direction: column;
                        }
                        
                        .process-arrow {
                            transform: rotate(90deg);
                        }
                    }
                `}</style>
            </Layout>
        </>
    );
}